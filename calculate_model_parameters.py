#!/usr/bin/env python3
"""
当前WNN-MRNN模型参数量计算脚本

这个脚本用于快速计算当前项目中WNN-MRNN模型的参数量，
支持多种数据集配置和参数组合分析。

使用方法：python calculate_model_parameters.py
"""

import os
import sys
import yaml
import torch
import torch.nn as nn
import numpy as np
import pandas as pd
from itertools import product
import json
from datetime import datetime

# 导入模型
from models.wnn_mrnn import WNN_MRNN

def count_trainable_parameters(model):
    """计算模型的可训练参数数量"""
    trainable_params = sum(p.numel() for p in model.parameters() if p.requires_grad)
    total_params = sum(p.numel() for p in model.parameters())
    return trainable_params, total_params

def count_parameters_by_module(model):
    """按模块统计参数量"""
    module_params = {}
    for name, module in model.named_modules():
        if len(list(module.children())) == 0:  # 叶子模块
            params = sum(p.numel() for p in module.parameters())
            if params > 0:
                module_params[name] = params
    return module_params

def load_config(config_path='config.yaml'):
    """加载配置文件"""
    with open(config_path, 'r', encoding='utf-8') as f:
        config = yaml.safe_load(f)
    return config

def create_model_with_config(dataset_type, config, custom_params=None):
    """
    根据配置文件和数据集类型创建模型
    
    Args:
        dataset_type: 数据集类型
        config: 配置文件
        custom_params: 自定义参数覆盖
    
    Returns:
        model: 创建的模型
        model_params: 模型参数字典
    """
    # 获取数据集特定配置
    if dataset_type in config['model']['dataset_specific_params']:
        dataset_config = config['model']['dataset_specific_params'][dataset_type]
    else:
        # 使用默认配置
        dataset_config = {
            'wavelet_dim': 64,
            'rnn_dim': 64,
            'num_layers': 2,
            'num_levels': 2,
            'dropout': 0.1
        }
    
    # 获取数据集信息
    sequence_length = config['data']['sequence_lengths'][dataset_type]
    
    # 根据数据集确定类别数
    if dataset_type == 'rml':
        num_classes = 11
    elif dataset_type == 'rml201801a':
        num_classes = 24
    elif dataset_type == 'hisar':
        num_classes = 26
    elif dataset_type.startswith('torchsig'):
        num_classes = 25
    else:
        num_classes = config['model']['num_classes']
    
    # 应用自定义参数
    if custom_params:
        dataset_config.update(custom_params)
    
    # 创建模型参数字典
    model_params = {
        'in_channels': config['model']['in_channels'],
        'num_classes': num_classes,
        'wavelet_dim': dataset_config['wavelet_dim'],
        'rnn_dim': dataset_config['rnn_dim'],
        'num_layers': dataset_config['num_layers'],
        'num_levels': dataset_config['num_levels'],
        'd_state': config['model']['d_state'],
        'msb_depth': config['model']['msb_depth'],
        'drop_rate': dataset_config['dropout'],
        'd_conv': config['model']['d_conv'],
        'expand': config['model']['expand']
    }
    
    # 创建模型
    model = WNN_MRNN(**model_params)
    
    return model, model_params

def analyze_current_model():
    """分析当前配置的模型参数量"""
    print("当前WNN-MRNN模型参数量分析")
    print("=" * 60)
    
    # 加载配置文件
    try:
        config = load_config()
        print("成功加载配置文件")
    except Exception as e:
        print(f"加载配置文件失败: {e}")
        return
    
    # 获取当前数据集类型
    current_dataset = config['data']['dataset_type']
    print(f"当前数据集: {current_dataset}")
    
    # 创建当前配置的模型
    try:
        model, model_params = create_model_with_config(current_dataset, config)
        
        # 计算参数量
        trainable_params, total_params = count_trainable_parameters(model)
        
        print(f"\n模型配置:")
        print("-" * 40)
        for key, value in model_params.items():
            print(f"  {key}: {value}")
        
        print(f"\n参数量统计:")
        print("-" * 40)
        print(f"  可训练参数: {trainable_params:,}")
        print(f"  总参数量: {total_params:,}")
        print(f"  可训练比例: {trainable_params/total_params:.4f}")
        
        # 按模块统计参数量
        module_params = count_parameters_by_module(model)
        print(f"\n按模块统计参数量:")
        print("-" * 40)
        sorted_modules = sorted(module_params.items(), key=lambda x: x[1], reverse=True)
        for name, params in sorted_modules[:10]:  # 显示前10个最大的模块
            print(f"  {name}: {params:,}")
        
        return {
            'dataset': current_dataset,
            'model_params': model_params,
            'trainable_params': trainable_params,
            'total_params': total_params,
            'module_params': module_params
        }
        
    except Exception as e:
        print(f"创建模型失败: {e}")
        import traceback
        traceback.print_exc()
        return None

def calculate_parameters_for_all_datasets():
    """计算所有数据集配置下的参数量"""
    print("\n所有数据集配置的参数量对比")
    print("=" * 60)
    
    # 加载配置文件
    try:
        config = load_config()
    except Exception as e:
        print(f"加载配置文件失败: {e}")
        return
    
    results = []
    
    # 遍历所有数据集类型
    dataset_types = ['rml', 'rml201801a', 'hisar', 'torchsig1024', 'torchsig2048', 'torchsig4096']
    
    for dataset_type in dataset_types:
        if dataset_type not in config['data']['sequence_lengths']:
            print(f"跳过未配置的数据集: {dataset_type}")
            continue
            
        try:
            model, model_params = create_model_with_config(dataset_type, config)
            trainable_params, total_params = count_trainable_parameters(model)
            
            result = {
                'dataset': dataset_type,
                'sequence_length': config['data']['sequence_lengths'][dataset_type],
                'num_classes': model_params['num_classes'],
                'wavelet_dim': model_params['wavelet_dim'],
                'rnn_dim': model_params['rnn_dim'],
                'num_layers': model_params['num_layers'],
                'num_levels': model_params['num_levels'],
                'trainable_params': trainable_params,
                'total_params': total_params,
                'trainable_ratio': trainable_params / total_params if total_params > 0 else 0
            }
            results.append(result)
            
            print(f"{dataset_type:12s}: 序列长度={result['sequence_length']:4d}, "
                  f"类别数={result['num_classes']:2d}, 参数量={trainable_params:8,}")
            
        except Exception as e:
            print(f"计算 {dataset_type} 失败: {e}")
            continue
    
    return results

def save_analysis_results(current_result, all_results):
    """保存分析结果到文件"""
    # 创建输出目录
    output_dir = 'parameter_analysis'
    os.makedirs(output_dir, exist_ok=True)
    
    # 生成时间戳
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    
    # 保存当前模型分析结果
    if current_result:
        current_file = os.path.join(output_dir, f'current_model_analysis_{timestamp}.json')
        with open(current_file, 'w', encoding='utf-8') as f:
            json.dump(current_result, f, indent=2, ensure_ascii=False)
        print(f"\n当前模型分析结果已保存到: {current_file}")
    
    # 保存所有数据集对比结果
    if all_results:
        # 保存到CSV
        df = pd.DataFrame(all_results)
        csv_file = os.path.join(output_dir, f'all_datasets_comparison_{timestamp}.csv')
        df.to_csv(csv_file, index=False, encoding='utf-8-sig')
        
        # 保存到JSON
        json_file = os.path.join(output_dir, f'all_datasets_comparison_{timestamp}.json')
        with open(json_file, 'w', encoding='utf-8') as f:
            json.dump(all_results, f, indent=2, ensure_ascii=False)
        
        print(f"所有数据集对比结果已保存到:")
        print(f"  CSV: {csv_file}")
        print(f"  JSON: {json_file}")
        
        return df, csv_file, json_file
    
    return None, None, None

def main():
    """主函数"""
    print("WNN-MRNN模型参数量分析工具")
    print("=" * 50)
    
    try:
        # 分析当前模型
        current_result = analyze_current_model()
        
        # 计算所有数据集配置的参数量
        all_results = calculate_parameters_for_all_datasets()
        
        # 保存结果
        df, csv_file, json_file = save_analysis_results(current_result, all_results)
        
        if df is not None:
            print(f"\n汇总统计:")
            print("-" * 40)
            print(f"总共分析了 {len(all_results)} 种数据集配置")
            print(f"参数量范围: {df['trainable_params'].min():,} - {df['trainable_params'].max():,}")
            print(f"平均参数量: {df['trainable_params'].mean():,.0f}")
        
    except Exception as e:
        print(f"分析过程中出现错误: {e}")
        import traceback
        traceback.print_exc()

def calculate_parameter_sensitivity():
    """分析参数对模型参数量的敏感性"""
    print("\n参数敏感性分析")
    print("=" * 60)

    # 加载配置文件
    try:
        config = load_config()
    except Exception as e:
        print(f"加载配置文件失败: {e}")
        return

    # 使用当前数据集类型
    current_dataset = config['data']['dataset_type']

    # 参数变化范围
    param_ranges = {
        'wavelet_dim': [32, 64, 128, 256],
        'rnn_dim': [32, 64, 128, 256],
        'num_levels': [1, 2, 3, 4],
        'num_layers': [1, 2, 3, 4]
    }

    sensitivity_results = []

    # 获取基准配置
    base_model, base_params = create_model_with_config(current_dataset, config)
    base_trainable, base_total = count_trainable_parameters(base_model)

    print(f"基准配置 ({current_dataset}):")
    print(f"  wavelet_dim={base_params['wavelet_dim']}, rnn_dim={base_params['rnn_dim']}")
    print(f"  num_levels={base_params['num_levels']}, num_layers={base_params['num_layers']}")
    print(f"  基准参数量: {base_trainable:,}")
    print()

    # 分析每个参数的影响
    for param_name, param_values in param_ranges.items():
        print(f"分析 {param_name} 的影响:")
        print("-" * 30)

        for value in param_values:
            try:
                # 创建自定义参数
                custom_params = {param_name: value}

                # 创建模型
                model, model_params = create_model_with_config(current_dataset, config, custom_params)
                trainable_params, total_params = count_trainable_parameters(model)

                # 计算变化比例
                change_ratio = (trainable_params - base_trainable) / base_trainable * 100

                result = {
                    'dataset': current_dataset,
                    'param_name': param_name,
                    'param_value': value,
                    'trainable_params': trainable_params,
                    'change_from_base': trainable_params - base_trainable,
                    'change_ratio': change_ratio
                }
                sensitivity_results.append(result)

                print(f"  {param_name}={value:3d}: {trainable_params:8,} ({change_ratio:+6.1f}%)")

            except Exception as e:
                print(f"  {param_name}={value:3d}: 计算失败 - {e}")

        print()

    return sensitivity_results

def generate_parameter_recommendations():
    """生成参数配置建议"""
    print("\n参数配置建议")
    print("=" * 60)

    # 加载配置文件
    try:
        config = load_config()
    except Exception as e:
        print(f"加载配置文件失败: {e}")
        return

    recommendations = {}

    # 为每个数据集生成建议
    dataset_types = ['rml', 'rml201801a', 'hisar', 'torchsig1024', 'torchsig2048', 'torchsig4096']

    for dataset_type in dataset_types:
        if dataset_type not in config['data']['sequence_lengths']:
            continue

        sequence_length = config['data']['sequence_lengths'][dataset_type]

        # 根据序列长度和复杂度给出建议
        if sequence_length <= 128:
            # 短序列
            recommended = {
                'wavelet_dim': 64,
                'rnn_dim': 64,
                'num_levels': 2,
                'num_layers': 2,
                'reason': '短序列，使用较小的模型避免过拟合'
            }
        elif sequence_length <= 1024:
            # 中等序列
            recommended = {
                'wavelet_dim': 128,
                'rnn_dim': 128,
                'num_levels': 3,
                'num_layers': 3,
                'reason': '中等序列，平衡模型复杂度和性能'
            }
        elif sequence_length <= 2048:
            # 长序列
            recommended = {
                'wavelet_dim': 128,
                'rnn_dim': 256,
                'num_levels': 2,
                'num_layers': 4,
                'reason': '长序列，增加RNN维度和层数处理复杂时序'
            }
        else:
            # 超长序列
            recommended = {
                'wavelet_dim': 256,
                'rnn_dim': 256,
                'num_levels': 1,
                'num_layers': 3,
                'reason': '超长序列，减少小波层数，增加特征维度'
            }

        recommendations[dataset_type] = recommended

        # 计算推荐配置的参数量
        try:
            model, _ = create_model_with_config(dataset_type, config, recommended)
            trainable_params, _ = count_trainable_parameters(model)
            recommended['estimated_params'] = trainable_params
        except:
            recommended['estimated_params'] = 'N/A'

        print(f"{dataset_type} (序列长度: {sequence_length}):")
        print(f"  推荐配置: wavelet_dim={recommended['wavelet_dim']}, rnn_dim={recommended['rnn_dim']}")
        print(f"            num_levels={recommended['num_levels']}, num_layers={recommended['num_layers']}")
        print(f"  预估参数量: {recommended['estimated_params']:,}" if isinstance(recommended['estimated_params'], int) else f"  预估参数量: {recommended['estimated_params']}")
        print(f"  建议理由: {recommended['reason']}")
        print()

    return recommendations

def compare_with_baseline():
    """与基线模型对比"""
    print("\n与基线模型对比")
    print("=" * 60)

    # 加载配置文件
    try:
        config = load_config()
    except Exception as e:
        print(f"加载配置文件失败: {e}")
        return

    current_dataset = config['data']['dataset_type']

    # 定义几种基线配置
    baseline_configs = {
        'minimal': {'wavelet_dim': 32, 'rnn_dim': 32, 'num_levels': 1, 'num_layers': 1},
        'small': {'wavelet_dim': 64, 'rnn_dim': 64, 'num_levels': 2, 'num_layers': 2},
        'medium': {'wavelet_dim': 128, 'rnn_dim': 128, 'num_levels': 3, 'num_layers': 3},
        'large': {'wavelet_dim': 256, 'rnn_dim': 256, 'num_levels': 4, 'num_layers': 4}
    }

    # 获取当前配置
    current_model, current_params = create_model_with_config(current_dataset, config)
    current_trainable, _ = count_trainable_parameters(current_model)

    print(f"当前配置 ({current_dataset}):")
    print(f"  参数量: {current_trainable:,}")
    print()

    comparison_results = []

    for config_name, baseline_params in baseline_configs.items():
        try:
            model, _ = create_model_with_config(current_dataset, config, baseline_params)
            trainable_params, _ = count_trainable_parameters(model)

            ratio = trainable_params / current_trainable

            result = {
                'config_name': config_name,
                'params': baseline_params,
                'trainable_params': trainable_params,
                'ratio_to_current': ratio
            }
            comparison_results.append(result)

            print(f"{config_name:8s}: {trainable_params:8,} ({ratio:.2f}x)")

        except Exception as e:
            print(f"{config_name:8s}: 计算失败 - {e}")

    return comparison_results

if __name__ == '__main__':
    main()

    # 运行额外的分析
    print("\n" + "="*80)
    print("扩展分析")
    print("="*80)

    # 参数敏感性分析
    sensitivity_results = calculate_parameter_sensitivity()

    # 生成参数建议
    recommendations = generate_parameter_recommendations()

    # 与基线对比
    comparison_results = compare_with_baseline()

    # 保存扩展分析结果
    if any([sensitivity_results, recommendations, comparison_results]):
        output_dir = 'parameter_analysis'
        os.makedirs(output_dir, exist_ok=True)
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")

        extended_results = {
            'sensitivity_analysis': sensitivity_results,
            'recommendations': recommendations,
            'baseline_comparison': comparison_results,
            'timestamp': timestamp
        }

        extended_file = os.path.join(output_dir, f'extended_analysis_{timestamp}.json')
        with open(extended_file, 'w', encoding='utf-8') as f:
            json.dump(extended_results, f, indent=2, ensure_ascii=False)

        print(f"\n扩展分析结果已保存到: {extended_file}")
