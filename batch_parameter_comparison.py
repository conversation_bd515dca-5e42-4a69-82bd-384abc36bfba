#!/usr/bin/env python3
"""
批量参数对比脚本

这个脚本用于批量对比不同参数配置下的WNN-MRNN模型参数量，
帮助选择最优的模型配置。

使用方法：python batch_parameter_comparison.py
"""

import os
import yaml
import torch
import pandas as pd
from itertools import product
from datetime import datetime
from models.wnn_mrnn import WNN_MRNN

def load_config(config_path='config.yaml'):
    """加载配置文件"""
    with open(config_path, 'r', encoding='utf-8') as f:
        config = yaml.safe_load(f)
    return config

def count_parameters(model):
    """计算模型参数量"""
    trainable = sum(p.numel() for p in model.parameters() if p.requires_grad)
    total = sum(p.numel() for p in model.parameters())
    return trainable, total

def create_model_with_params(wavelet_dim, rnn_dim, num_levels, num_layers, 
                           num_classes, d_state=16, msb_depth=1, 
                           dropout=0.1, d_conv=4, expand=2):
    """根据参数创建模型"""
    model = WNN_MRNN(
        in_channels=2,
        num_classes=num_classes,
        wavelet_dim=wavelet_dim,
        rnn_dim=rnn_dim,
        num_layers=num_layers,
        num_levels=num_levels,
        d_state=d_state,
        msb_depth=msb_depth,
        drop_rate=dropout,
        d_conv=d_conv,
        expand=expand
    )
    return model

def get_dataset_info():
    """获取数据集信息"""
    return {
        'rml': {'sequence_length': 128, 'num_classes': 11},
        'rml201801a': {'sequence_length': 1024, 'num_classes': 24},
        'hisar': {'sequence_length': 1024, 'num_classes': 26},
        'torchsig1024': {'sequence_length': 1024, 'num_classes': 25},
        'torchsig2048': {'sequence_length': 2048, 'num_classes': 25},
        'torchsig4096': {'sequence_length': 4096, 'num_classes': 25},
    }

def define_parameter_ranges():
    """定义参数搜索范围"""
    return {
        'wavelet_dim': [32, 64, 128, 256],
        'rnn_dim': [32, 64, 128, 256],
        'num_levels': [1, 2, 3, 4],
        'num_layers': [1, 2, 3, 4]
    }

def batch_calculate_parameters(dataset_name=None, max_combinations=50):
    """批量计算参数量"""
    print("批量参数量计算")
    print("=" * 60)
    
    # 获取数据集信息
    datasets = get_dataset_info()
    
    # 如果指定了数据集，只计算该数据集
    if dataset_name and dataset_name in datasets:
        datasets = {dataset_name: datasets[dataset_name]}
    
    # 获取参数范围
    param_ranges = define_parameter_ranges()
    
    results = []
    total_combinations = len(list(product(*param_ranges.values()))) * len(datasets)
    
    print(f"总计算组合数: {total_combinations}")
    if total_combinations > max_combinations:
        print(f"限制计算组合数为: {max_combinations}")
    
    current_count = 0
    
    for dataset, dataset_info in datasets.items():
        print(f"\n计算数据集: {dataset}")
        print(f"序列长度: {dataset_info['sequence_length']}, 类别数: {dataset_info['num_classes']}")
        print("-" * 40)
        
        dataset_results = []
        
        # 生成参数组合
        param_combinations = list(product(
            param_ranges['wavelet_dim'],
            param_ranges['rnn_dim'],
            param_ranges['num_levels'],
            param_ranges['num_layers']
        ))
        
        # 如果组合太多，随机采样
        if len(param_combinations) > max_combinations // len(datasets):
            import random
            random.seed(42)
            param_combinations = random.sample(param_combinations, max_combinations // len(datasets))
        
        for wavelet_dim, rnn_dim, num_levels, num_layers in param_combinations:
            current_count += 1
            
            try:
                # 创建模型
                model = create_model_with_params(
                    wavelet_dim=wavelet_dim,
                    rnn_dim=rnn_dim,
                    num_levels=num_levels,
                    num_layers=num_layers,
                    num_classes=dataset_info['num_classes']
                )
                
                # 计算参数量
                trainable_params, total_params = count_parameters(model)
                
                result = {
                    'dataset': dataset,
                    'sequence_length': dataset_info['sequence_length'],
                    'num_classes': dataset_info['num_classes'],
                    'wavelet_dim': wavelet_dim,
                    'rnn_dim': rnn_dim,
                    'num_levels': num_levels,
                    'num_layers': num_layers,
                    'trainable_params': trainable_params,
                    'total_params': total_params,
                    'params_per_class': trainable_params / dataset_info['num_classes'],
                    'params_per_length': trainable_params / dataset_info['sequence_length']
                }
                
                results.append(result)
                dataset_results.append(result)
                
                # 显示进度
                if current_count % 10 == 0 or current_count <= 5:
                    print(f"[{current_count:3d}] wavelet={wavelet_dim:3d}, rnn={rnn_dim:3d}, "
                          f"levels={num_levels}, layers={num_layers} -> {trainable_params:,}")
                
            except Exception as e:
                print(f"计算失败: wavelet={wavelet_dim}, rnn={rnn_dim}, "
                      f"levels={num_levels}, layers={num_layers} - {e}")
                continue
        
        # 显示该数据集的统计
        if dataset_results:
            params_list = [r['trainable_params'] for r in dataset_results]
            print(f"\n{dataset} 统计:")
            print(f"  计算成功: {len(dataset_results)} 个配置")
            print(f"  参数量范围: {min(params_list):,} - {max(params_list):,}")
            print(f"  平均参数量: {sum(params_list)/len(params_list):,.0f}")
    
    return results

def analyze_results(results):
    """分析结果"""
    if not results:
        print("没有结果可分析")
        return
    
    df = pd.DataFrame(results)
    
    print(f"\n结果分析")
    print("=" * 60)
    
    # 总体统计
    print(f"总计算配置数: {len(results)}")
    print(f"涵盖数据集: {', '.join(df['dataset'].unique())}")
    print(f"参数量范围: {df['trainable_params'].min():,} - {df['trainable_params'].max():,}")
    print(f"平均参数量: {df['trainable_params'].mean():,.0f}")
    
    # 按数据集统计
    print(f"\n按数据集统计:")
    print("-" * 40)
    for dataset in df['dataset'].unique():
        dataset_df = df[df['dataset'] == dataset]
        print(f"{dataset:12s}: {len(dataset_df):3d} 配置, "
              f"参数量 {dataset_df['trainable_params'].min():,} - {dataset_df['trainable_params'].max():,}")
    
    # 参数影响分析
    print(f"\n参数影响分析 (平均参数量):")
    print("-" * 40)
    
    for param in ['wavelet_dim', 'rnn_dim', 'num_levels', 'num_layers']:
        param_impact = df.groupby(param)['trainable_params'].mean()
        print(f"{param}:")
        for value, avg_params in param_impact.items():
            print(f"  {value:3d}: {avg_params:8,.0f}")
    
    # 找出最优配置
    print(f"\n推荐配置:")
    print("-" * 40)
    
    for dataset in df['dataset'].unique():
        dataset_df = df[df['dataset'] == dataset]
        
        # 找出参数量适中的配置（避免过大或过小）
        median_params = dataset_df['trainable_params'].median()
        
        # 选择接近中位数的配置
        best_idx = (dataset_df['trainable_params'] - median_params).abs().idxmin()
        best_config = dataset_df.loc[best_idx]
        
        print(f"{dataset}:")
        print(f"  推荐: wavelet_dim={best_config['wavelet_dim']}, rnn_dim={best_config['rnn_dim']}")
        print(f"        num_levels={best_config['num_levels']}, num_layers={best_config['num_layers']}")
        print(f"  参数量: {best_config['trainable_params']:,}")
    
    return df

def save_results(results, df):
    """保存结果"""
    if not results:
        return
    
    # 创建输出目录
    output_dir = 'parameter_analysis'
    os.makedirs(output_dir, exist_ok=True)
    
    # 生成时间戳
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    
    # 保存详细结果
    csv_file = os.path.join(output_dir, f'batch_parameter_comparison_{timestamp}.csv')
    df.to_csv(csv_file, index=False, encoding='utf-8-sig')
    
    # 保存汇总统计
    summary_stats = {
        'total_configurations': len(results),
        'datasets': list(df['dataset'].unique()),
        'parameter_ranges': {
            'trainable_params': {
                'min': int(df['trainable_params'].min()),
                'max': int(df['trainable_params'].max()),
                'mean': float(df['trainable_params'].mean()),
                'median': float(df['trainable_params'].median())
            }
        },
        'by_dataset': {}
    }
    
    for dataset in df['dataset'].unique():
        dataset_df = df[df['dataset'] == dataset]
        summary_stats['by_dataset'][dataset] = {
            'configurations': len(dataset_df),
            'min_params': int(dataset_df['trainable_params'].min()),
            'max_params': int(dataset_df['trainable_params'].max()),
            'mean_params': float(dataset_df['trainable_params'].mean())
        }
    
    import json
    summary_file = os.path.join(output_dir, f'batch_comparison_summary_{timestamp}.json')
    with open(summary_file, 'w', encoding='utf-8') as f:
        json.dump(summary_stats, f, indent=2, ensure_ascii=False)
    
    print(f"\n结果已保存:")
    print(f"  详细结果: {csv_file}")
    print(f"  汇总统计: {summary_file}")

def main():
    """主函数"""
    print("WNN-MRNN 批量参数对比工具")
    print("=" * 50)
    
    try:
        # 批量计算参数量
        results = batch_calculate_parameters(max_combinations=100)
        
        if results:
            # 分析结果
            df = analyze_results(results)
            
            # 保存结果
            save_results(results, df)
        else:
            print("没有成功计算任何配置")
    
    except Exception as e:
        print(f"计算过程中出现错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == '__main__':
    main()
