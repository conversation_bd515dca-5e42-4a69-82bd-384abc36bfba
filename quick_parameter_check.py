#!/usr/bin/env python3
"""
快速参数量检查脚本

这个脚本用于快速查看当前配置下WNN-MRNN模型的参数量，
无需复杂的分析，适合日常使用。

使用方法：python quick_parameter_check.py
"""

import os
import yaml
import torch
from models.wnn_mrnn import WNN_MRNN

def load_config(config_path='config.yaml'):
    """加载配置文件"""
    with open(config_path, 'r', encoding='utf-8') as f:
        config = yaml.safe_load(f)
    return config

def count_parameters(model):
    """计算模型参数量"""
    trainable = sum(p.numel() for p in model.parameters() if p.requires_grad)
    total = sum(p.numel() for p in model.parameters())
    return trainable, total

def format_number(num):
    """格式化数字显示"""
    if num >= 1e6:
        return f"{num/1e6:.2f}M"
    elif num >= 1e3:
        return f"{num/1e3:.2f}K"
    else:
        return str(num)

def get_model_info(config):
    """获取模型信息"""
    dataset_type = config['data']['dataset_type']
    
    # 获取数据集特定配置
    if dataset_type in config['model']['dataset_specific_params']:
        dataset_config = config['model']['dataset_specific_params'][dataset_type]
    else:
        # 使用默认配置
        dataset_config = {
            'wavelet_dim': 64,
            'rnn_dim': 64,
            'num_layers': 2,
            'num_levels': 2,
            'dropout': 0.1
        }
    
    # 获取序列长度和类别数
    sequence_length = config['data']['sequence_lengths'][dataset_type]
    
    if dataset_type == 'rml':
        num_classes = 11
    elif dataset_type == 'rml201801a':
        num_classes = 24
    elif dataset_type == 'hisar':
        num_classes = 26
    elif dataset_type.startswith('torchsig'):
        num_classes = 25
    else:
        num_classes = config['model']['num_classes']
    
    return {
        'dataset_type': dataset_type,
        'sequence_length': sequence_length,
        'num_classes': num_classes,
        'wavelet_dim': dataset_config['wavelet_dim'],
        'rnn_dim': dataset_config['rnn_dim'],
        'num_layers': dataset_config['num_layers'],
        'num_levels': dataset_config['num_levels'],
        'dropout': dataset_config['dropout'],
        'd_state': config['model']['d_state'],
        'msb_depth': config['model']['msb_depth'],
        'd_conv': config['model']['d_conv'],
        'expand': config['model']['expand']
    }

def create_model(model_info):
    """创建模型"""
    model = WNN_MRNN(
        in_channels=2,
        num_classes=model_info['num_classes'],
        wavelet_dim=model_info['wavelet_dim'],
        rnn_dim=model_info['rnn_dim'],
        num_layers=model_info['num_layers'],
        num_levels=model_info['num_levels'],
        d_state=model_info['d_state'],
        msb_depth=model_info['msb_depth'],
        drop_rate=model_info['dropout'],
        d_conv=model_info['d_conv'],
        expand=model_info['expand']
    )
    return model

def print_model_summary(model_info, trainable_params, total_params):
    """打印模型摘要"""
    print("WNN-MRNN 模型参数摘要")
    print("=" * 50)
    
    print(f"数据集类型: {model_info['dataset_type']}")
    print(f"序列长度: {model_info['sequence_length']}")
    print(f"类别数: {model_info['num_classes']}")
    
    print(f"\n模型配置:")
    print(f"  小波维度 (wavelet_dim): {model_info['wavelet_dim']}")
    print(f"  RNN维度 (rnn_dim): {model_info['rnn_dim']}")
    print(f"  小波层数 (num_levels): {model_info['num_levels']}")
    print(f"  MRNN层数 (num_layers): {model_info['num_layers']}")
    print(f"  MSB深度 (msb_depth): {model_info['msb_depth']}")
    print(f"  状态维度 (d_state): {model_info['d_state']}")
    print(f"  卷积核大小 (d_conv): {model_info['d_conv']}")
    print(f"  扩展因子 (expand): {model_info['expand']}")
    print(f"  Dropout率: {model_info['dropout']}")
    
    print(f"\n参数统计:")
    print(f"  可训练参数: {trainable_params:,} ({format_number(trainable_params)})")
    print(f"  总参数量: {total_params:,} ({format_number(total_params)})")
    print(f"  可训练比例: {trainable_params/total_params:.4f}")
    
    # 计算模型大小估算（假设float32）
    model_size_mb = (total_params * 4) / (1024 * 1024)
    print(f"  估算模型大小: {model_size_mb:.2f} MB")

def get_module_parameters(model):
    """获取各模块的参数量"""
    module_params = {}
    
    for name, module in model.named_modules():
        if len(list(module.children())) == 0:  # 叶子模块
            params = sum(p.numel() for p in module.parameters())
            if params > 0:
                module_params[name] = params
    
    return module_params

def print_module_breakdown(module_params):
    """打印模块参数分解"""
    print(f"\n主要模块参数分布:")
    print("-" * 40)
    
    # 按参数量排序
    sorted_modules = sorted(module_params.items(), key=lambda x: x[1], reverse=True)
    
    # 只显示前10个最大的模块
    for i, (name, params) in enumerate(sorted_modules[:10]):
        percentage = params / sum(module_params.values()) * 100
        print(f"  {i+1:2d}. {name}: {params:,} ({percentage:.1f}%)")

def main():
    """主函数"""
    try:
        # 加载配置
        config = load_config()
        
        # 获取模型信息
        model_info = get_model_info(config)
        
        # 创建模型
        model = create_model(model_info)
        
        # 计算参数量
        trainable_params, total_params = count_parameters(model)
        
        # 打印摘要
        print_model_summary(model_info, trainable_params, total_params)
        
        # 获取模块参数分布
        module_params = get_module_parameters(model)
        
        # 打印模块分解
        print_module_breakdown(module_params)
        
        print(f"\n" + "=" * 50)
        print("检查完成！")
        
    except FileNotFoundError:
        print("错误: 找不到配置文件 config.yaml")
        print("请确保在项目根目录下运行此脚本")
    except Exception as e:
        print(f"错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == '__main__':
    main()
